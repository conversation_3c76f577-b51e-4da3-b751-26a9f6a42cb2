<template>
    <view class="container">
        <!-- 视频素材列表 -->
        <view class="media-list-container">
            <!-- 紧凑型视频列表项 -->
            <view v-for="(media, index) in filteredMedia" :key="index" class="media-item video-item"
                @click="viewMediaDetail(media)">

                <view class="media-thumbnail video-thumbnail">
                    <image :src="media.thumbnail" mode="aspectFill" class="media-thumbnail-image"></image>
                    <text class="video-duration">{{ media.duration }}</text>
                    <u-tag :text="getStatusText(media)" :type="getStatusType(media)" size="mini"
                        class="video-status"></u-tag>
                </view>

                <view class="media-content">
                    <view class="media-title">{{ media.title }}</view>
                    <view class="media-meta">
                        <text class="media-meta-item">{{ media.uploader }}</text>
                        <text class="media-meta-item">{{ formatDate(media.uploadTime) }}</text>
                    </view>
                </view>
            </view>

            <!-- 空状态 -->
            <u-empty v-if="filteredMedia.length === 0" mode="list" :text="`暂无${getStatusLabel(currentStatus)}视频`"
                iconSize="120" textSize="16" marginTop="100">
                <u-button type="primary" text="上传视频" @click="showUploadModal" size="normal" shape="round"></u-button>
            </u-empty>
        </view>

        <!-- 悬浮按钮组 -->
        <FloatingActionButton text="上传" type="primary" :initialPosition="{ right: 20, bottom: 180 }"
            @click="showUploadModal" />

    </view>
</template>

<script>

import FloatingActionButton from '@/components/FloatingActionButton.vue';
import { queryVideos } from "@/api/video.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
    mixins: [mediaCommonMixin],
    components: {
        FloatingActionButton
    },
    data () {
        return {
            mediaList: [],
            currentStatus: 'all'
        }
    },
    computed: {
        filteredMedia () {
            let result = this.mediaList;

            // 按状态筛选
            if (this.currentStatus !== 'all') {
                result = result.filter(media => media.status === this.currentStatus);
            }

            return result;
        }
    },
    created () {
        // 加载视频
        this.loadAllMedia();

        // 监听刷新事件
        uni.$on('refreshVideoList', () => {
            this.loadAllMedia();
        });
    },

    beforeDestroy () {
        // 移除事件监听
        uni.$off('refreshVideoList');
    },

    methods: {
        async loadAllMedia () {
            try {
                this.showLoading("加载中...");

                const response = await queryVideos({
                    page: 1,
                    pageSize: 1000
                });

                if (response.success && response.data) {
                    this.mediaList = response.data.items.map(video => ({
                        id: video.id,
                        title: video.title,
                        description: video.description,
                        thumbnail: this.buildCompleteFileUrl(video.coverUrl) || '/assets/images/video-cover.jpg',
                        duration: this.formatDuration(video.duration),
                        uploader: video.uploader || '管理员',
                        uploadTime: video.uploadTime,
                        status: video.status,
                        videoUrl: this.buildCompleteFileUrl(video.videoUrl),
                        fileSize: video.fileSize,
                        views: video.views || 0,
                        likes: video.likes || 0,
                        comments: video.comments || 0
                    }));
                } else {
                    console.error('获取视频列表失败:', response.message);
                    this.showError(response.message || "获取视频列表失败");
                    this.mediaList = [];
                }

                this.hideLoading();
            } catch (error) {
                console.error('加载视频列表失败:', error);
                this.hideLoading();
                this.showError("加载失败");
                this.mediaList = [];
            }
        },

        showUploadModal () {
            this.safeNavigateTo('/pages/admin/media/upload');
        },

        viewMediaDetail (media) {
            this.safeNavigateTo(`/pages/admin/media/detail?id=${media.id}`);
        },



        // 获取状态文本
        getStatusText (media) {
            const statusMap = {
                0: '已下架',
                1: '已上架',
                2: '处理失败',
                3: '压缩中'
            };
            return statusMap[media.status] || '未知状态';
        },

        // 获取状态类型（用于u-tag的type属性）
        getStatusType (media) {
            const typeMap = {
                0: 'error',    // 已下架
                1: 'success',  // 已上架
                2: 'error',    // 处理失败
                3: 'warning'   // 压缩中
            };
            return typeMap[media.status] || 'info';
        },

        // 获取状态标签文本
        getStatusLabel (status) {
            const labelMap = {
                'all': '',
                0: '已下架',
                1: '已上架',
                2: '处理失败',
                3: '压缩中'
            };
            return labelMap[status] || '';
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/styles/index.scss';

.container {
    padding: 0;
    background-color: #f7f7f7;
}

/* 媒体列表容器 */
.media-list-container {
    padding: 20rpx;
    padding-top: 0;
}

/* 媒体项通用样式 */
.media-item {
    display: flex;
    background-color: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    padding: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.media-item:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 缩略图样式 */
.media-thumbnail {
    position: relative;
    border-radius: 12rpx;
    overflow: hidden;
    margin-right: 20rpx;
}

.video-thumbnail {
    width: 160rpx;
    height: 120rpx;
}



.media-thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 视频时长标签 */
.video-duration {
    position: absolute;
    bottom: 8rpx;
    right: 8rpx;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    font-size: 20rpx;
    padding: 4rpx 8rpx;
    border-radius: 6rpx;
}

/* 状态标签 */
.video-status {
    position: absolute;
    top: 8rpx;
    left: 8rpx;
}

/* 内容区域 */
.media-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.media-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 12rpx;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
}

.media-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    margin-bottom: 8rpx;
}

.media-meta-item {
    font-size: 22rpx;
    color: #999;
}
</style>
