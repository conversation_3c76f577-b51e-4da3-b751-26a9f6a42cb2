<template>
  <view class="container">
    <!-- 固定的用户信息卡片 -->
    <view class="fixed-user-section">
      <view class="custom-card" v-if="userInfo">
        <!-- 用户头像和基本信息 -->
        <view class="user-header">
          <!-- 调试信息 -->
          <view style="font-size: 12px; color: #999; margin-bottom: 10px;" v-if="userInfo">
            <div>原始头像: {{ userInfo.avatar }}</div>
            <div>处理后头像: {{ userInfo.avatar && buildCompleteFileUrl(userInfo.avatar) }}</div>
            <div>显示文字: {{ userInfo.username?.charAt(0)?.toUpperCase() || 'U' }}</div>
          </view>

          <view class="user-avatar-section">
            <!-- 测试直接使用img标签 -->
            <img v-if="userInfo.avatar" :src="buildCompleteFileUrl(userInfo.avatar)"
              style="width: 60px; height: 60px; border-radius: 50%; margin-right: 10px;" @load="onImageLoad"
              @error="onImageError" />

            <u-avatar :src="userInfo.avatar && buildCompleteFileUrl(userInfo.avatar)"
              :text="userInfo.username?.charAt(0)?.toUpperCase() || 'U'" size="60" shape="circle" bg-color="#f0f9ff"
              color="#186BFF" :randomBgColor="!userInfo.avatar" />
          </view>

          <view class="user-basic-info">
            <view class="user-name-row">
              <text class="user-name" @tap.stop="copyUsername(userInfo.username)">{{ userInfo.username ||
                userInfo.nickname || '未设置' }}</text>
              <u-tag :text="userInfo.type === 'promoter' ? '推广员' : '用户'"
                :type="userInfo.type === 'promoter' ? 'warning' : 'info'" shape="circle" size="mini" />
            </view>
            <view class="user-id">
              <text class="id-label">ID: </text>
              <text class="id-value">{{ userInfo.id }}</text>
              <u-button type="primary" size="mini" shape="circle" @click.stop="copyUserId"
                :customStyle="{ marginLeft: '16rpx' }">
                <u-icon name="copy" size="12" color="#fff" />
                <text style="margin-left: 8rpx; font-size: 20rpx;">复制</text>
              </u-button>
            </view>
          </view>
        </view>

        <!-- 详细信息区域 -->
        <view class="user-details">
          <view class="detail-grid">
            <view class="detail-item">
              <u-icon name="calendar" size="20" color="#186BFF" />
              <view class="detail-content">
                <text class="detail-label">注册时间</text>
                <text class="detail-value">{{ formatDate(userInfo.registerTime) }}</text>
              </view>
            </view>
            <view class="detail-item">
              <u-icon name="clock" size="20" color="#186BFF" />
              <view class="detail-content">
                <text class="detail-label">最后登录</text>
                <text class="detail-value">{{ formatDate(userInfo.lastLoginTime) || '从未登录' }}</text>
              </view>
            </view>
            <view class="detail-item" v-if="userInfo.employeeId">
              <u-icon name="account" size="20" color="#186BFF" />
              <view class="detail-content">
                <text class="detail-label">员工ID</text>
                <text class="detail-value">{{ userInfo.employeeId }}</text>
              </view>
            </view>
            <view class="detail-item" v-if="userInfo.type === 'promoter'">
              <u-icon name="account-fill" size="20" color="#186BFF" />
              <view class="detail-content">
                <text class="detail-label">推广用户</text>
                <text class="detail-value">{{ relatedUsers.length }}人</text>
              </view>
            </view>
            <view class="detail-item" v-else>
              <u-icon name="play-circle" size="20" color="#186BFF" />
              <view class="detail-content">
                <text class="detail-label">观看视频</text>
                <text class="detail-value">{{ userInfo.watchedVideos || 0 }}个</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 时间筛选 -->
        <template v-if="userInfo.type === 'promoter'">
          <TimeFilter v-model="activeTimeFilter" @change="handleTimeFilterChange" />
        </template>
      </view>
    </view>

    <!-- 可滚动的内容区域 -->
    <scroll-view class="scrollable-content" scroll-y="true">
      <!-- 学习记录 -->
      <view class="learning-records" v-if="userInfo && userInfo.type !== 'promoter'">
        <view class="section-title">学习记录 ({{ learningRecords.length }})</view>

        <view class="record-list">
          <view class="learning-item" v-for="(record, index) in learningRecords" :key="index">
            <!-- 视频信息 -->
            <view class="video-info">
              <view class="video-cover"
                :style="{ backgroundImage: `url(${record.cover && !record.cover.startsWith('/static') ? buildCompleteFileUrl(record.cover) : record.cover})` }">
              </view>
              <view class="video-details">
                <view class="video-title">{{ record.title }}</view>
                <view class="video-time">{{ record.watchTime }}</view>
              </view>
            </view>

            <!-- 详细信息 -->
            <view class="activity-details">
              <view class="detail-item">
                <text class="detail-text">观看进度</text>
                <text class="detail-value">{{ record.duration }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-text">答题结果</text>
                <text class="detail-value">{{ record.quizCompleted ? (record.quizResult ? '答对' : '答错') : '未答题' }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-text">获得奖励</text>
                <text class="detail-value">{{ record.rewardReceived ? record.rewardAmount + '元' : '0.00元' }}</text>
              </view>
            </view>
          </view>

          <u-empty v-if="learningRecords.length === 0" mode="data" icon="inbox" text="暂无学习记录" :iconSize="80"
            :textSize="28" iconColor="#c8c9cc" textColor="#909399" />
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>

import UserInfoCard from "../../../components/UserInfoCard.vue";
import TimeFilter from "../../../components/TimeFilter.vue";
import { getVideoUserDetail } from "../../../api/video-user.js";
import { getUserRecords } from "../../../api/user-batch-record.js";
import { formatUserData } from "../../../utils/employee-data-mapper.js";
import { apiCallWrapper, ErrorHandlerPresets } from "../../../utils/api-error-handler.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
  mixins: [mediaCommonMixin],
  components: {
    UserInfoCard,
    TimeFilter,
  },
  data () {
    return {
      userId: null,
      promoterId: null,
      userInfo: null,
      relatedUsers: [],
      activeTimeFilter: "today",
      customDateRange: {
        startDate: "",
        endDate: "",
      },
      learningRecords: [],
      loaded: false,
      scrollViewHeight: 400, // 默认高度
    };
  },
  computed: {
    pageTitle () {
      if (!this.userInfo) return "用户信息";
      return this.userInfo.type === "promoter" ? "推广员信息" : "用户信息";
    },
  },
  onLoad (options) {
    // 可以通过用户ID或推广员ID加载
    if (options.userId) {
      this.userId = parseInt(options.userId);
      this.loadUserData();
    } else if (options.promoterId) {
      this.promoterId = parseInt(options.promoterId);
      this.loadPromoterData();
    } else {
      uni.showToast({
        title: "参数错误",
        icon: "none",
      });
      setTimeout(() => {
        this.goBack();
      }, 1500);
    }

    // 计算滚动视图高度
    this.calculateScrollViewHeight();
  },

  onReady () {
    // 页面渲染完成后重新计算高度
    this.calculateScrollViewHeight();
  },
  methods: {
    // 计算滚动视图高度
    calculateScrollViewHeight () {
      uni.getSystemInfo({
        success: (res) => {
          // 获取屏幕高度
          const screenHeight = res.windowHeight;
          // 减去固定部分的高度（标题栏 + 用户信息卡片 + 学习记录标题）
          // 这里估算固定部分大约占用 300px，可以根据实际情况调整
          this.scrollViewHeight = screenHeight - 300;
        }
      });
    },

    // 处理时间筛选变化
    handleTimeFilterChange (timeRange) {
      this.activeTimeFilter = timeRange;
      this.customDateRange = {
        startDate: timeRange.startDate,
        endDate: timeRange.endDate
      };
      console.log("时间筛选变化:", timeRange);
      // 这里可以根据需要执行其他操作，如重新加载数据等
    },

    async loadUserData () {
      if (!this.userId) {
        uni.showToast({
          title: "用户ID不能为空",
          icon: "none",
        });
        setTimeout(() => {
          this.goBack();
        }, 1500);
        return;
      }

      try {
        const response = await apiCallWrapper(
          () => getVideoUserDetail(this.userId),
          {
            ...ErrorHandlerPresets.important,
            loadingTitle: '加载用户信息...',
            errorTitle: '加载失败'
          }
        );

        if (response.success && response.data) {
          this.userInfo = formatUserData(response.data);
          this.userInfo.type = "user"; // 设置用户类型

          // 加载用户的观看记录
          await this.loadUserWatchRecords();
        } else {
          uni.showToast({
            title: response.msg || "获取用户信息失败",
            icon: "none",
          });
          setTimeout(() => {
            this.goBack();
          }, 1500);
        }
      } catch (error) {
        console.error('加载用户数据失败:', error);
        uni.showToast({
          title: "加载用户信息失败",
          icon: "none",
        });
        setTimeout(() => {
          this.goBack();
        }, 1500);
      }
    },

    async loadPromoterData () {
      if (!this.promoterId) {
        uni.showToast({
          title: "推广员ID不能为空",
          icon: "none",
        });
        setTimeout(() => {
          this.goBack();
        }, 1500);
        return;
      }

      try {
        const response = await apiCallWrapper(
          () => getVideoUserDetail(this.promoterId),
          {
            ...ErrorHandlerPresets.important,
            loadingTitle: '加载推广员信息...',
            errorTitle: '加载失败'
          }
        );

        if (response.success && response.data) {
          this.userInfo = formatUserData(response.data);
          this.userInfo.type = "promoter"; // 设置推广员类型
          console.log('推广员详情数据:', this.userInfo);

          // 加载推广员的相关用户
          await this.loadRelatedUsers();
        } else {
          uni.showToast({
            title: response.msg || "获取推广员信息失败",
            icon: "none",
          });
          setTimeout(() => {
            this.goBack();
          }, 1500);
        }
      } catch (error) {
        console.error('加载推广员数据失败:', error);
        uni.showToast({
          title: "加载推广员信息失败",
          icon: "none",
        });
        setTimeout(() => {
          this.goBack();
        }, 1500);
      }
    },

    async loadRelatedUsers () {
      // 如果是推广员，加载其推广的用户列表
      if (this.userInfo && this.userInfo.type === "promoter") {
        try {
          // 这里可以调用API获取推广员的相关用户
          // 暂时设置为空数组，后续可以根据需要实现
          this.relatedUsers = [];
        } catch (error) {
          console.error('加载相关用户失败:', error);
          this.relatedUsers = [];
        }
      }
    },

    async loadUserWatchRecords () {
      if (!this.userId) return;

      try {
        const response = await apiCallWrapper(
          () => getUserRecords(this.userId),
          {
            ...ErrorHandlerPresets.silent,
            loadingTitle: '加载观看记录...'
          }
        );

        if (response.success && response.data) {
          this.learningRecords = this.formatWatchRecords(response.data);
        } else {
          console.warn('获取观看记录失败:', response.msg);
          this.learningRecords = [];
        }
      } catch (error) {
        console.error('加载观看记录失败:', error);
        this.learningRecords = [];
      }
    },

    formatWatchRecords (records) {
      if (!Array.isArray(records)) return [];

      return records.map(record => {
        return {
          title: record.batchName || '未知视频',
          cover: record.videoCoverUrl ? this.buildCompleteFileUrl(record.videoCoverUrl) : '/static/images/video-cover.jpg',
          watchTime: this.formatDateTime(record.createTime),
          duration: this.formatProgressText(record.watchProgressPercent),
          watched: record.watchProgressPercent > 0,
          quizCompleted: record.hasAnswered || false,
          rewardReceived: record.rewardStatus === 1, // 1表示发放成功
          quizResult: record.correctRate > 0.5, // 正确率大于50%算答对
          rewardAmount: record.rewardAmount || 0,
          watchProgress: record.watchProgressPercent / 100, // 转换为0-1的小数
          isCompleted: record.isCompleted
        };
      }).sort((a, b) => new Date(b.watchTime) - new Date(a.watchTime));
    },

    formatProgressText (progressPercent) {
      if (!progressPercent || progressPercent <= 0) return '未观看';
      if (progressPercent >= 100) return '已完播';
      return `观看进度 ${progressPercent.toFixed(1)}%`;
    },

    generateMockRecords () {
      // TODO: 调用API获取学习记录
      const videos = [];
      const recordCount = Math.min(this.userInfo.watchedVideos || 0, 5);

      // 格式化日期时间的通用函数
      const formatDateTime = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hour = String(date.getHours()).padStart(2, "0");
        const minute = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hour}:${minute}`;
      };

      for (let i = 0; i < recordCount; i++) {
        const randomVideo = videos[Math.floor(Math.random() * videos.length)];
        const today = new Date();
        const watchDate = new Date(today);
        watchDate.setDate(today.getDate() - Math.floor(Math.random() * 7)); // 最近7天内

        // 随机决定是否完成了答题和获得奖励
        const watched = true; // 既然有观看记录，肯定是观看了的
        const quizCompleted = Math.random() > 0.3; // 70%的概率完成答题
        const rewardReceived = quizCompleted && Math.random() > 0.2; // 答题完成且80%概率获得奖励
        const quizResult = quizCompleted ? Math.random() > 0.2 : false; // 80%正确率

        this.learningRecords.push({
          title: randomVideo.title,
          cover: randomVideo.cover || "/static/images/video-cover.jpg",
          watchTime: formatDateTime(watchDate),
          duration: randomVideo.duration,
          watched: watched,
          quizCompleted: quizCompleted,
          rewardReceived: rewardReceived,
          quizResult: quizResult,
          rewardAmount: rewardReceived ? 0.5 : 0,
        });
      }

      // 按时间排序（最新的在前面）
      this.learningRecords.sort((a, b) => new Date(b.watchTime) - new Date(a.watchTime));
    },

    viewUserDetail (user) {
      uni.navigateTo({
        url: `/pages/admin/users/info?userId=${user.id}`,
      });
    },

    viewPromoter (promoterId) {
      uni.navigateTo({
        url: `/pages/admin/users/info?promoterId=${promoterId}`,
      });
    },

    goBack () {
      uni.navigateBack();
    },

    // 图片加载调试方法
    onImageLoad (event) {
      console.log('✅ 图片加载成功:', event.target.src);
    },

    onImageError (event) {
      console.log('❌ 图片加载失败:', event.target.src);
      console.log('错误详情:', event);
    },

    copyUsername (username) {
      // 复制用户名到剪贴板
      uni.setClipboardData({
        data: username,
        success: () => {
          uni.showToast({
            title: '用户名已复制',
            icon: 'success',
            duration: 1500
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none',
            duration: 1500
          });
        }
      });
    },

    copyUserId () {
      // 复制用户ID到剪贴板
      uni.setClipboardData({
        data: String(this.userInfo.id),
        success: () => {
          uni.showToast({
            title: 'ID已复制',
            icon: 'success',
            duration: 1500
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none',
            duration: 1500
          });
        }
      });
    },

    formatDate (dateString) {
      if (!dateString) return "";

      // 处理日期格式
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")}`;
    },

    formatDateTime (dateString) {
      if (!dateString) return "";

      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hour = String(date.getHours()).padStart(2, "0");
      const minute = String(date.getMinutes()).padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}`;
    },


  },
};
</script>

<style lang="scss">
@import '../../../styles/index.scss';

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-secondary;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 固定的用户信息区域 */
.fixed-user-section {
  position: relative;
  z-index: $z-index-sticky;
  flex-shrink: 0;
}

/* 自定义卡片样式 - 替代 u-card */
.custom-card {
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  margin: 16rpx;
  overflow: hidden;
  margin-top: 0;
}

/* 可滚动的内容区域 */
.scrollable-content {
  flex: 1;
  height: 0;
}

/* 用户头像和基本信息 */
.user-header {
  display: flex;
  padding: $spacing-base $spacing-lg;
  align-items: center;
}

.user-avatar-section {
  margin-right: $spacing-base;
}

.user-basic-info {
  flex: 1;
}

.user-name-row {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-sm;
}

.user-name {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $text-primary;
  margin-right: $spacing-base;
  cursor: pointer;
}

.user-id {
  display: flex;
  align-items: center;
}

.id-label {
  font-size: $font-size-sm;
  color: $text-secondary;
}

.id-value {
  font-size: $font-size-sm;
  color: $text-primary;
  margin-right: $spacing-sm;
}

/* 详细信息区域 */
.user-details {
  padding: 0 $spacing-lg $spacing-base;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-sm;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  padding: $spacing-sm;
  background: $bg-primary;
  border-radius: $border-radius-base;
  border: 1rpx solid $border-secondary;
}

.detail-content {
  flex: 1;
  margin-left: $spacing-sm;
}

.detail-label {
  display: block;
  font-size: $font-size-xs;
  color: $text-tertiary;
  margin-bottom: 2rpx;
}

.detail-value {
  display: block;
  font-size: $font-size-sm;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

/* 学习记录区域 */
.learning-records {
  padding: $spacing-sm $spacing-base;
}

.section-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $text-primary;
  padding: $spacing-base;
  background: $bg-primary;
  border-radius: $border-radius-md $border-radius-md 0 0;
  border-bottom: 1rpx solid $border-secondary;
  margin-bottom: 0;
}

/* 记录列表 */
.record-list {
  background: $bg-primary;
  border-radius: 0 0 $border-radius-md $border-radius-md;
  overflow: hidden;
}

/* 学习记录项 */
.learning-item {
  padding: $spacing-base;
  border-bottom: 1rpx solid $border-secondary;
  position: relative;
  transition: background-color 0.3s ease;
}

.learning-item:last-child {
  border-bottom: none;
}

.learning-item:active {
  background-color: $bg-secondary;
}

/* 视频信息 */
.video-info {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-base;
}

.video-cover {
  width: 96rpx;
  height: 96rpx;
  border-radius: $border-radius-md;
  background-size: cover;
  background-position: center;
  margin-right: $spacing-base;
  flex-shrink: 0;
  border: 1rpx solid $border-secondary;
  box-shadow: $shadow-sm;
  background-color: $bg-secondary;
}

.video-details {
  flex: 1;
}

.video-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  color: $text-primary;
  margin-bottom: $spacing-sm;
  line-height: 1.4;
}

.video-time {
  font-size: $font-size-sm;
  color: $text-secondary;
  background: $bg-secondary;
  padding: 4rpx $spacing-sm;
  border-radius: $border-radius-sm;
  display: inline-block;
}

/* 活动详情 */
.activity-details {
  display: flex;
  gap: $spacing-base;
  padding: $spacing-base 0 0;
  border-top: 1rpx solid $border-secondary;
}

.activity-details .detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
  padding: $spacing-sm;
  background: $bg-secondary;
  border-radius: $border-radius-sm;
  border: 1rpx solid $border-secondary;
}

.activity-details .detail-text {
  font-size: $font-size-xs;
  color: $text-secondary;
  margin-bottom: $spacing-xs;
  font-weight: $font-weight-medium;
}

.activity-details .detail-value {
  font-size: $font-size-sm;
  color: $primary-color;
  font-weight: $font-weight-bold;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .detail-grid {
    grid-template-columns: 1fr;
    gap: $spacing-base;
  }

  .user-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .user-avatar-section {
    margin-right: 0;
    margin-bottom: $spacing-lg;
  }
}
</style>