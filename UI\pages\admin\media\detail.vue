<template>
	<view class="page-container">
		<!-- 视频预览区域 -->
		<view class="video-preview-section">
			<!-- 压缩中状态显示遮罩 -->
			<view v-if="videoInfo.status === 'compressing'" class="video-processing-overlay">
				<view class="processing-content">
					<u-loading-icon mode="circle" size="80" color="#186BFF"></u-loading-icon>
					<text class="processing-text">视频压缩中</text>
					<text class="processing-desc">正在为您优化视频质量，请稍后再试</text>
				</view>
			</view>
			<!-- 处理失败状态显示遮罩 -->
			<view v-else-if="videoInfo.status === 'failed'" class="video-failed-overlay">
				<view class="failed-content">
					<view class="failed-icon">
						<u-icon name="close-circle-fill" size="80" color="#ff4d4f"></u-icon>
					</view>
					<text class="failed-text">视频处理失败</text>
					<text class="failed-desc">请检查视频格式后重新上传</text>
				</view>
			</view>
			<!-- 正常视频播放器 -->
			<video v-else :src="videoInfo.url" :poster="videoInfo.cover" class="video-player" controls></video>
			<u-tag :text="getStatusText(videoInfo)" :type="getStatusType(videoInfo)" class="video-status-tag"></u-tag>
		</view>

		<!-- 视频信息区域 -->
		<view class="content-section">
			<view class="video-info-card">
				<view class="video-header">
					<view class="video-title-section">
						<text class="video-title">{{ videoInfo.title }}</text>
						<view class="video-meta">
							<view class="meta-item">
								<u-icon name="clock" size="24" color="#8c8c8c"></u-icon>
								<text class="meta-text">{{ formatDate(videoInfo.uploadTime) }}</text>
							</view>
							<view class="meta-item">
								<u-icon name="account" size="24" color="#8c8c8c"></u-icon>
								<text class="meta-text">{{ videoInfo.uploader }}</text>
							</view>
							<view class="meta-item">
								<u-icon name="play-circle" size="24" color="#8c8c8c"></u-icon>
								<text class="meta-text">{{ videoInfo.views }} 次观看</text>
							</view>
						</view>
					</view>
					<view class="action-button">
						<u-button text="操作" type="primary" size="small" @click="showActionSheet"
							:custom-style="{ borderRadius: '20rpx', fontSize: '24rpx' }"></u-button>
					</view>
				</view>

				<view class="video-desc" v-if="videoInfo.description">
					<view class="desc-header">
						<u-icon name="file-text" size="28" color="#595959"></u-icon>
						<text class="desc-title">视频描述</text>
					</view>
					<text class="desc-content">{{ videoInfo.description }}</text>
				</view>
			</view>
		</view>

		<!-- 相关问题区域 -->
		<view class="content-section" v-if="quizzes.length > 0">
			<view class="quiz-section">
				<view class="quiz-section-header">
					<view class="quiz-title-wrapper">
						<u-icon name="help-circle" size="32" color="#186BFF"></u-icon>
						<text class="quiz-section-title">相关问题</text>
						<view class="quiz-count">{{ quizzes.length }}题</view>
					</view>
					<view class="reward-tag">
						<u-icon name="gift" size="24" color="#ff4d4f"></u-icon>
						<text class="reward-text">{{ totalReward }}元</text>
					</view>
				</view>

				<view class="quiz-list">
					<view class="quiz-item" v-for="(quiz, index) in quizzes" :key="quiz.id">
						<view class="quiz-item-header">
							<view class="quiz-number-badge">
								<text class="quiz-number">{{ index + 1 }}</text>
							</view>
							<view class="quiz-type-tag">
								<u-icon name="radio-button-checked" size="20" color="#52c41a"></u-icon>
								<text class="quiz-type-text">单选题</text>
							</view>
						</view>

						<view class="quiz-question-wrapper">
							<text class="quiz-question">{{ quiz.question }}</text>
						</view>

						<view class="quiz-options">
							<view class="option-item" v-for="option in quiz.options" :key="option.id"
								:class="{ 'correct-option-item': isCorrectOption(quiz, option.id) }">
								<view class="option-prefix">{{ option.id }}</view>
								<text class="option-text">{{ option.text }}</text>
								<view v-if="isCorrectOption(quiz, option.id)" class="correct-badge">
									<u-icon name="checkmark-circle-fill" size="24" color="#52c41a"></u-icon>
								</view>
							</view>
						</view>

						<view class="quiz-answer-section">
							<view class="correct-answer-wrapper">
								<u-icon name="checkmark-circle" size="20" color="#52c41a"></u-icon>
								<text class="answer-label">正确答案：</text>
								<text class="answer-value">{{ quiz.correctAnswer }}</text>
							</view>

							<view class="quiz-explanation" v-if="quiz.explanation">
								<u-icon name="information-circle" size="20" color="#186BFF"></u-icon>
								<text class="explanation-content">{{ quiz.explanation }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 删除确认弹窗 -->
		<u-popup :show="showDeletePopup" mode="center" :closeOnClickOverlay="true" @close="cancelDelete">
			<view class="delete-popup-container">
				<view class="delete-popup-header">
					<u-icon name="error-circle-fill" size="48" color="#ff4d4f"></u-icon>
					<text class="delete-popup-title">确认删除视频</text>
				</view>
				<view class="delete-popup-body">
					<text class="delete-popup-message">确定要删除"{{ videoInfo.title }}"吗？</text>
					<text class="delete-popup-desc">删除后将无法恢复，相关的问题和奖励也会被删除</text>
				</view>
				<view class="delete-popup-footer">
					<u-button text="取消" type="default" @click="cancelDelete" size="large"
						:custom-style="{ flex: 1, marginRight: '16rpx', borderRadius: '12rpx' }"></u-button>
					<u-button text="确认删除" type="error" @click="handleDelete" size="large"
						:custom-style="{ flex: 1, borderRadius: '12rpx' }"></u-button>
				</view>
			</view>
		</u-popup>

		<!-- 操作选择弹窗 -->
		<u-action-sheet :show="showActionPopup" :actions="actionList" @close="closeActionSheet"
			@select="handleActionSelect" title="选择操作" cancelText="取消"></u-action-sheet>
	</view>
</template>

<script>
import { getVideoDetail, deleteVideo } from "@/api/video.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
	mixins: [mediaCommonMixin],
	data () {
		return {
			videoId: 0,
			videoInfo: {},
			quizzes: [],
			showDeletePopup: false,
			showActionPopup: false,
			actionList: [
				{
					name: '编辑视频',
					icon: 'edit-pen',
					color: '#3c9cff'
				},
				{
					name: '创建批次',
					icon: 'plus-circle',
					color: '#5ac725'
				},
				{
					name: '删除视频',
					icon: 'trash',
					color: '#f56c6c'
				}
			]
		}
	},
	computed: {
		quizCount () {
			return this.quizzes.length;
		},
		totalReward () {
			return this.videoInfo.rewardAmount || 0;
		},
		rewardText () {
			return '奖励: ' + this.totalReward + '元';
		}
	},
	onLoad (options) {
		if (options.id) {
			this.videoId = parseInt(options.id);
			this.loadVideoInfo();
		} else {
			uni.showToast({
				title: '参数错误',
				icon: 'none'
			});
			setTimeout(() => {
				this.goBack();
			}, 1500);
		}
	},
	onShow () {
		if (this.videoId) {
			this.loadVideoInfo();
		}
	},
	methods: {
		async loadVideoInfo () {
			try {
				uni.showLoading({
					title: "加载中...",
				});

				const response = await getVideoDetail(this.videoId);

				if (response.success && response.data) {
					const video = response.data;
					this.videoInfo = {
						id: video.id,
						title: video.title,
						cover: this.buildCompleteFileUrl(video.coverUrl) || '/assets/images/video-cover.jpg',
						url: this.buildCompleteFileUrl(video.videoUrl) || 'https://www.runoob.com/try/demo_source/mov_bbb.mp4',
						duration: this.formatDuration(video.duration),
						uploadTime: video.createTime,
						uploader: video.creatorName || '未知',
						uploaderId: video.creatorId || '',
						views: video.viewCount || 0,
						likes: video.likeCount || 0,
						description: video.description || '',
						status: this.mapVideoStatus(video.status),
						rewardAmount: video.rewardAmount || 0
					};

					this.processQuestions(video.questions || []);
				} else {
					throw new Error(response.msg || '获取视频详情失败');
				}

				uni.hideLoading();
			} catch (error) {
				console.error('加载视频详情失败:', error);
				uni.hideLoading();

				uni.showToast({
					title: '加载视频详情失败',
					icon: 'none'
				});
			}
		},

		mapVideoStatus (apiStatus) {
			const statusMap = {
				0: 'offline',      // 下架
				1: 'online',       // 上架
				2: 'failed',       // 失败
				3: 'compressing'   // 压缩中
			};
			return statusMap[apiStatus] || 'offline';
		},





		processQuestions (questions) {
			try {
				if (questions && questions.length > 0) {
					this.quizzes = questions.map((q, index) => {
						const options = (q.options || []).map((option, optIndex) => ({
							id: String.fromCharCode(65 + optIndex),
							text: option.optionText || option.text || `选项${optIndex + 1}`
						}));

						let correctAnswer = 'A';
						if (q.options && q.options.length > 0) {
							const correctOption = q.options.find(opt => opt.isCorrect);
							if (correctOption) {
								const correctIndex = q.options.indexOf(correctOption);
								correctAnswer = String.fromCharCode(65 + correctIndex);
							}
						}

						return {
							id: q.id || (index + 1),
							question: q.questionText || q.question || `问题${index + 1}`,
							options: options,
							correctAnswer: correctAnswer,
							explanation: q.explanation || ''
						};
					});
				} else {
					this.quizzes = [];
				}
			} catch (error) {
				console.error('处理问题数据失败:', error);
				this.quizzes = [];
			}
		},

		editVideo () {
			uni.navigateTo({
				url: `/pages/admin/media/upload?id=${this.videoId}`
			});
		},

		goBack () {
			uni.navigateBack();
		},

		showDeleteConfirm () {
			this.showDeletePopup = true;
		},

		cancelDelete () {
			this.showDeletePopup = false;
		},

		async handleDelete () {
			this.showDeletePopup = false;

			try {
				// 显示加载提示
				uni.showLoading({
					title: '删除中...'
				});

				// 调用删除API
				const response = await deleteVideo(this.videoId);

				uni.hideLoading();

				if (response.success) {
					// 删除成功
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});

					// 使用事件机制确保页面返回后刷新
					setTimeout(() => {
						// 发送全局事件通知列表页面刷新
						uni.$emit('videoDeleted', { videoId: this.videoId });

						// 返回上一页
						uni.navigateBack();
					}, 1500);
				} else {
					// 删除失败
					uni.showToast({
						title: response.msg || '删除失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('删除视频失败:', error);
				uni.hideLoading();

				uni.showToast({
					title: '删除失败，请重试',
					icon: 'none'
				});
			}
		},

		createBatch () {
			uni.navigateTo({
				url: `/pages/admin/media/publish?id=${this.videoId}`
			});
		},

		isCorrectOption (quiz, optionId) {
			if (typeof quiz.correctAnswer === 'string') {
				return quiz.correctAnswer === optionId;
			} else if (Array.isArray(quiz.correctAnswer)) {
				return quiz.correctAnswer.includes(optionId);
			}
			return false;
		},

		// 显示操作选择弹窗
		showActionSheet () {
			this.showActionPopup = true;
		},

		// 关闭操作选择弹窗
		closeActionSheet () {
			this.showActionPopup = false;
		},

		// 处理操作选择
		handleActionSelect (item) {
			this.showActionPopup = false;

			switch (item.name) {
				case '编辑视频':
					this.editVideo();
					break;
				case '创建批次':
					this.createBatch();
					break;
				case '删除视频':
					this.showDeleteConfirm();
					break;
				default:
					break;
			}
		}
	}
}
</script>

<style lang="scss">
@import '@/styles/variables.scss';

.page-container {
	background-color: $bg-secondary;
	min-height: 100vh;
	padding-bottom: $spacing-xl;
}

.video-preview-section {
	width: 100%;
	height: 420rpx;
	background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
	position: relative;
	border-radius: 0 0 $border-radius-lg $border-radius-lg;
	overflow: hidden;
}

.video-player {
	width: 100%;
	height: 100%;
	border-radius: 0 0 $border-radius-lg $border-radius-lg;
}

.video-status-tag {
	position: absolute;
	top: $spacing-lg;
	right: $spacing-lg;
	z-index: 10;
	backdrop-filter: blur(8rpx);
	background: rgba(255, 255, 255, 0.9);
	border-radius: $border-radius-base;
}

.video-processing-overlay,
.video-failed-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(24, 107, 255, 0.9) 0%, rgba(24, 107, 255, 0.7) 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 5;
	backdrop-filter: blur(10rpx);
}

.video-failed-overlay {
	background: linear-gradient(135deg, rgba(255, 77, 79, 0.9) 0%, rgba(255, 77, 79, 0.7) 100%);
}

.processing-content,
.failed-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	padding: $spacing-xl;
}

.processing-text,
.failed-text {
	color: $text-white;
	font-size: $font-size-xl;
	font-weight: $font-weight-semibold;
	margin-top: $spacing-lg;
	margin-bottom: $spacing-sm;
}

.processing-desc,
.failed-desc {
	color: rgba(255, 255, 255, 0.8);
	font-size: $font-size-md;
	line-height: $line-height-relaxed;
}

.failed-icon {
	margin-bottom: $spacing-base;
}

.content-section {
	margin-bottom: $spacing-sm;
}

.video-info {
	margin-bottom: $spacing-sm;
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.info-item:last-child {
	margin-bottom: 0;
}

.info-label {
	width: 140rpx;
	font-size: $font-size-sm;
	color: $text-secondary;
}

.info-value {
	font-size: $font-size-sm;
	color: $text-primary;
}

.video-desc {
	margin-top: $spacing-base;
	padding-top: $spacing-base;
	border-top: 1rpx solid $border-secondary;
	font-size: $font-size-base;
	color: $text-primary;
	line-height: $line-height-relaxed;
}

.quiz-item {
	border-bottom: 1rpx solid $border-secondary;
	padding-bottom: $spacing-base;
	margin-bottom: $spacing-base;
}

.quiz-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.quiz-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: $spacing-sm;
}

.quiz-number {
	font-size: $font-size-md;
	color: $text-primary;
	font-weight: $font-weight-semibold;
}

.quiz-content {
	font-size: $font-size-md;
	color: $text-primary;
	line-height: $line-height-relaxed;
	margin-bottom: $spacing-lg;
	font-weight: $font-weight-medium;
}

.quiz-options {
	display: flex;
	flex-direction: column;
	gap: $spacing-base;
	margin-bottom: $spacing-base;
}

.option-item {
	background-color: $bg-secondary;
	border-radius: $border-radius-base;
	border: 1rpx solid $border-secondary;
	padding: $spacing-sm;
}

.option-text {
	font-size: $font-size-base;
	color: $text-primary;
}

.correct-option {
	color: $success-color;
	font-weight: $font-weight-semibold;
}

.correct-option-badge {
	display: inline-block;
	margin-left: 10rpx;
	color: $text-white;
	background-color: $success-color;
	border-radius: 50%;
	width: 36rpx;
	height: 36rpx;
	line-height: 36rpx;
	text-align: center;
	font-size: $font-size-sm;
}

.quiz-correct-answer {
	margin-bottom: $spacing-base;
	font-size: $font-size-sm;
	color: $text-secondary;
}

.correct-answer-label {
	font-weight: $font-weight-semibold;
}

.correct-answer-value {
	margin-left: $spacing-sm;
}

.quiz-explanation {
	font-size: $font-size-sm;
	color: $text-secondary;
}

.explanation-label {
	font-weight: $font-weight-semibold;
	margin-right: 8rpx;
}

.explanation-content {
	margin-left: $spacing-sm;
}

.delete-popup-container {
	background-color: $bg-primary;
	border-radius: $border-radius-lg;
	width: 600rpx;
	max-width: 90vw;
	overflow: hidden;
	box-shadow: $shadow-lg;
}

.delete-popup-header {
	padding: $spacing-lg $spacing-lg $spacing-base $spacing-lg;
	border-bottom: 1rpx solid $border-secondary;
}

.delete-popup-title {
	font-size: $font-size-lg;
	font-weight: $font-weight-semibold;
	color: $text-primary;
	text-align: center;
}

.delete-popup-body {
	padding: $spacing-lg;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.delete-popup-icon {
	font-size: 80rpx;
	margin-bottom: $spacing-base;
}

.delete-popup-message {
	font-size: $font-size-md;
	color: $text-primary;
	margin-bottom: $spacing-sm;
	font-weight: $font-weight-medium;
}

.delete-popup-desc {
	font-size: $font-size-sm;
	color: $text-tertiary;
	line-height: $line-height-relaxed;
}

.delete-popup-footer {
	padding: $spacing-base $spacing-lg $spacing-lg $spacing-lg;
	display: flex;
	gap: $spacing-base;
	justify-content: center;
	border-top: 1rpx solid $border-secondary;
	background-color: $bg-secondary;
}

.operation-section {
	margin-top: $spacing-base;
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	top: -6rpx;
	right: 10rpx;
}

.quiz-card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.quiz-card-title {
	font-size: $font-size-lg;
	font-weight: $font-weight-semibold;
	color: $text-primary;
}
</style>